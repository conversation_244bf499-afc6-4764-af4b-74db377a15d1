# Langgraph MVP Implementation

This project serves as a template for building AI applications using the Vercel AI SDK, integrated with LangChain/LangGraph for advanced conversational AI capabilities.

## Features

*   **Vercel AI SDK Integration**: Leverage the power of Vercel's AI SDK for seamless AI model interactions.
*   **LangChain/LangGraph**: Utilize Lang<PERSON>hain and <PERSON>Grap<PERSON> for building complex conversational flows and agentic behaviors（all in src/graph/ and src/tools/）.
*   **Responsive Chat UI**: A user-friendly chat interface with proper markdown rendering for tables and other formats, consistent alignment, and responsive design.

## Getting Started

### Prerequisites
*   Node.js (v18 or higher)

### Installation
1.  Install dependencies:
    ```bash
    yarn
    ```

### Running the Application

```bash
yarn dev
```

Open [http://localhost:3000](http://localhost:3000) in your browser to see the result.

## Further Implementations

*   **Connect More Tools through Composio**: Extend the AI's capabilities by integrating additional tools and APIs via Composio.
*   **More Complex and Custom LangGraph Implementations**: Develop more sophisticated and tailored LangGraph workflows for advanced use cases.
