'use client';

import { type Message } from 'ai';
import { useChat } from '@ai-sdk/react';
import { useState, useRef, useCallback } from 'react';
import type { FormEvent, ReactNode } from 'react';
import { toast, Toaster } from 'sonner';
import { StickToBottom, useStickToBottomContext } from 'use-stick-to-bottom';
import { ArrowDown, ArrowUpIcon, LoaderCircle, X, Plus, FileText } from 'lucide-react';

import { ChatMessageBubble } from '@/components/ChatMessageBubble';
import { ThinkingIndicator } from '@/components/ThinkingIndicator';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { cn } from '@/utils/cn';
import { ModelSelector, type ModelConfig } from '@/components/ModelSelector';

interface FileAttachment {
  id: string;
  name: string;
  content: string;
  type: string;
}

function ChatMessages(props: {
  messages: Message[];
  emptyStateComponent: ReactNode;
  aiEmoji?: string;
  className?: string;
  showThinking?: boolean;
}) {
  return (
    <div className="flex flex-col max-w-[800px] mx-auto pb-12 w-full">
      {props.messages.map((m) => {
        return (
          <ChatMessageBubble
            key={m.id}
            message={m}
            aiEmoji={props.aiEmoji}
            isStreaming={false}
          />
        );
      })}
      {props.showThinking && (
        <div className="flex mt-6">
          <ThinkingIndicator isVisible={true} />
        </div>
      )}
    </div>
  );
}

function ScrollToBottom(props: { className?: string }) {
  const { isAtBottom, scrollToBottom } = useStickToBottomContext();

  if (isAtBottom) return null;
  return (
    <Button variant="outline" className={props.className} onClick={() => scrollToBottom()}>
      <ArrowDown className="w-4 h-4" />
      <span>Scroll to bottom</span>
    </Button>
  );
}

function CodingChatInput(props: {
  onSubmit: (e: FormEvent<HTMLFormElement>) => void;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  loading?: boolean;
  placeholder?: string;
  files: FileAttachment[];
  onFileAdd: (file: FileAttachment) => void;
  onFileRemove: (id: string) => void;
  className?: string;
  modelConfig?: ModelConfig;
  onModelChange?: (config: ModelConfig) => void;
}) {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      props.onSubmit(e as unknown as FormEvent<HTMLFormElement>);
    }
  };

  const handleFileSelect = useCallback((files: FileList | null) => {
    if (!files) return;

    Array.from(files).forEach(file => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        const fileAttachment: FileAttachment = {
          id: Math.random().toString(36).substring(2, 11),
          name: file.name,
          content: content,
          type: file.type
        };
        props.onFileAdd(fileAttachment);
      };
      reader.readAsText(file);
    });
  }, [props]);

  return (
    <form
      onSubmit={(e) => {
        e.stopPropagation();
        e.preventDefault();
        props.onSubmit(e);
      }}
      className={cn('flex w-full flex-col', props.className)}
    >
      {/* 文件上传区域 - 只在有文件时显示 */}
      {props.files.length > 0 && (
        <div className="mb-4 max-w-[800px] w-full mx-auto animate-slide-up">
          <div className="flex flex-wrap gap-3">
            {props.files.map(file => (
              <div key={file.id} className="flex items-center gap-2 bg-white/60 backdrop-blur-sm rounded-xl px-4 py-2.5 text-sm border border-white/50 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                <FileText className="h-4 w-4 text-blue-600" />
                <span className="truncate max-w-[150px] font-medium text-gray-700">{file.name}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 hover:bg-red-100 rounded-full transition-colors duration-200"
                  onClick={() => props.onFileRemove(file.id)}
                >
                  <X className="h-3 w-3 text-red-500" />
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 主输入框 */}
      <div className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-white/50 max-w-[800px] w-full mx-auto overflow-hidden chat-input-shadow animate-scale-in">
        <div className="flex items-end p-4">
          <Textarea
            value={props.value}
            placeholder={props.placeholder}
            onChange={props.onChange}
            onKeyDown={handleKeyDown}
            className="border-none outline-none bg-transparent flex-grow resize-none text-base leading-6 min-h-[24px] max-h-[200px] placeholder:text-gray-500 focus:placeholder:text-gray-400 transition-all duration-200"
            rows={1}
            style={{
              minHeight: '24px',
              maxHeight: '200px',
              lineHeight: '1.5'
            }}
          />

          <div className="flex items-center ml-3">
            <Button
              className="rounded-full p-2 h-10 w-10 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white transition-all duration-300 hover:scale-110 shadow-lg"
              type="submit"
              disabled={props.loading || (!props.value.trim() && props.files.length === 0)}
            >
              {props.loading ? <LoaderCircle className="animate-spin h-5 w-5" /> : <ArrowUpIcon className="h-5 w-5" />}
            </Button>
          </div>
        </div>

        {/* 底部工具栏 */}
        <div className="flex items-center justify-between px-4 pb-3 pt-1 border-t border-gray-100/50">
          <div className="flex items-center gap-3">
            <input
              ref={fileInputRef}
              type="file"
              multiple
              className="hidden"
              onChange={(e) => handleFileSelect(e.target.files)}
              accept=".txt,.js,.ts,.jsx,.tsx,.py,.java,.cpp,.c,.h,.css,.html,.json,.xml,.md,.sql,.sh,.yaml,.yml"
            />

            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 rounded-full hover:bg-blue-50 hover:text-blue-600 transition-all duration-200 hover:scale-110"
              onClick={() => fileInputRef.current?.click()}
              type="button"
            >
              <Plus className="h-4 w-4 text-gray-500" />
            </Button>

            {/* <span className="text-xs text-gray-400">
              支持代码文件、文档等多种格式
            </span> */}

            {/* 模型选择器 */}
            {props.modelConfig && props.onModelChange && (
              <ModelSelector
                value={props.modelConfig}
                onChange={props.onModelChange}
                disabled={props.loading}
              />
            )}

            {/* 图片上传 */}
            {/* <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 rounded-full hover:bg-gray-100"
              onClick={() => fileInputRef.current?.click()}
              type="button"
            >
              <Paperclip className="h-4 w-4 text-gray-600" />
            </Button> */}

            {/* 图片上传 */}
            {/* <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 rounded-full hover:bg-gray-100"
              type="button"
            >
              <Image className="h-4 w-4 text-gray-600" />
            </Button> */}

            {/* 语音输入 */}
            {/* <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 rounded-full hover:bg-gray-100"
              type="button"
            >
              <Mic className="h-4 w-4 text-gray-600" />
            </Button> */}
          </div>

          <div className="text-xs text-gray-400">
            {props.files.length > 0 && `${props.files.length} 个文件`}
          </div>
        </div>
      </div>
    </form>
  );
}

function StickyToBottomContent(props: {
  content: ReactNode;
  footer?: ReactNode;
  className?: string;
  contentClassName?: string;
}) {
  const context = useStickToBottomContext();

  return (
    <div
      ref={context.scrollRef}
      style={{ width: '100%', height: '100%' }}
      className={cn('grid grid-rows-[1fr,auto]', props.className)}
    >
      <div ref={context.contentRef} className={props.contentClassName}>
        {props.content}
      </div>

      {props.footer}
    </div>
  );
}

export function CodingChatWindow(props: {
  endpoint: string;
  emptyStateComponent: ReactNode;
  placeholder?: string;
  emoji?: string;
}) {
  const [files, setFiles] = useState<FileAttachment[]>([]);
  const [showThinking, setShowThinking] = useState(false);
  const [modelConfig, setModelConfig] = useState<ModelConfig>({ type: 'google' });

  const chat = useChat({
    api: props.endpoint,
    body: {
      modelConfig: modelConfig
    },
    onFinish(response: Message) {
      console.log('CodingChatWindow - Final response: ', response?.content);
      setShowThinking(false);
    },
    onResponse(response: Response) {
      console.log('CodingChatWindow - Response received. Status:', response.status);
      setShowThinking(false);
    },
    onError: (e: Error) => {
      console.error('CodingChatWindow - Error: ', e);
      setShowThinking(false);
      toast.error(`Error while processing your request`, { description: e.message });
    },
  });

  const handleFileAdd = useCallback((file: FileAttachment) => {
    setFiles(prev => [...prev, file]);
  }, []);

  const handleFileRemove = useCallback((id: string) => {
    setFiles(prev => prev.filter(f => f.id !== id));
  }, []);

  function isChatLoading(): boolean {
    return chat.status === 'streaming';
  }

  async function sendMessage(e: FormEvent<HTMLFormElement>) {
    e.preventDefault();
    if (isChatLoading()) return;

    // 开始思考状态
    setShowThinking(true);

    // 如果有文件，自定义消息内容
    if (files.length > 0) {
      const fileInfo = files.map(f => f.name).join(', ');
      const messageContent = chat.input.trim() || '请分析以下上传的文件并提供编程建议或优化方案。';
      const contentWithFiles = `${messageContent}\n\n📎 附件: ${fileInfo}`;

      // 保存当前文件数据
      const currentFiles = [...files];

      // 使用 append 方法发送自定义消息，并传递文件数据
      chat.append({
        role: 'user',
        content: contentWithFiles
      }, {
        body: {
          files: currentFiles
        }
      });

      // 清空输入框和文件
      chat.setInput('');
      setFiles([]);
    } else {
      // 没有文件时使用默认的 handleSubmit
      chat.handleSubmit(e);
    }
  }

  return (
    <>
      <StickToBottom>
        <StickyToBottomContent
          className="absolute inset-0"
          contentClassName="py-8 px-2"
          content={
            chat.messages.length === 0 ? (
              <div>{props.emptyStateComponent}</div>
            ) : (
              <ChatMessages
                aiEmoji={props.emoji}
                messages={chat.messages}
                emptyStateComponent={props.emptyStateComponent}
                showThinking={showThinking}
              />
            )
          }
          footer={
            <div className="sticky bottom-4 px-4">
              <ScrollToBottom className="absolute bottom-full left-1/2 -translate-x-1/2 mb-4" />
              <CodingChatInput
                value={chat.input}
                onChange={chat.handleInputChange}
                onSubmit={sendMessage}
                loading={isChatLoading()}
                placeholder={props.placeholder ?? '问问 Gemini'}
                files={files}
                onFileAdd={handleFileAdd}
                onFileRemove={handleFileRemove}
                modelConfig={modelConfig}
                onModelChange={setModelConfig}
              />
            </div>
          }
        />
      </StickToBottom>
      <Toaster />
    </>
  );
}
