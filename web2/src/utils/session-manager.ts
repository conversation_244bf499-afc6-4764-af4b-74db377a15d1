import { v4 as uuidv4 } from 'uuid';
import { get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>pointer, getMongoClient } from '@/config/mongodb';

export interface SessionConfig {
  thread_id: string;
  checkpoint_ns?: string;
  user_id?: string;
}

// 扩展的会话信息接口，基于LangGraph最佳实践
export interface SessionInfo {
  thread_id: string;
  user_id?: string;
  session_type: 'chat' | 'coding';
  title?: string;
  description?: string;
  tags: string[];

  // 时间信息
  created_at: Date;
  last_accessed: Date;
  updated_at: Date;

  // 统计信息
  message_count: number;
  checkpoint_count: number;
  total_tokens?: number;

  // 会话状态
  status: 'active' | 'archived' | 'deleted';
  is_favorite: boolean;

  // 元数据
  metadata: {
    model_config?: any;
    client_info?: {
      user_agent?: string;
      ip_address?: string;
    };
    performance?: {
      avg_response_time?: number;
      total_duration?: number;
      success_rate?: number;
      total_tokens?: number;
    };
  };

  // 最后一条消息预览
  last_message?: {
    content: string;
    role: 'user' | 'assistant';
    timestamp: Date;
  };
}

// 会话列表查询参数
export interface SessionListParams {
  user_id?: string;
  session_type?: 'chat' | 'coding';
  status?: 'active' | 'archived' | 'deleted';
  tags?: string[];
  search?: string;
  page?: number;
  limit?: number;
  sort_by?: 'created_at' | 'last_accessed' | 'message_count';
  sort_order?: 'asc' | 'desc';
}

// 会话统计信息
export interface SessionStats {
  total_sessions: number;
  active_sessions: number;
  archived_sessions: number;
  total_messages: number;
  avg_messages_per_session: number;
  most_active_day: string;
  session_types: {
    chat: number;
    coding: number;
  };
}

/**
 * 创建新的会话
 */
export function createSession(
  sessionType: 'chat' | 'coding' = 'chat',
  user_id?: string,
  title?: string
): SessionConfig {
  const thread_id = uuidv4();

  console.log(`SessionManager: 创建新会话 ${thread_id} (类型: ${sessionType}, 用户: ${user_id})`);

  return {
    thread_id,
    checkpoint_ns: '', // 默认命名空间
    user_id
  };
}

/**
 * 创建完整的会话信息
 */
export async function createSessionInfo(
  sessionType: 'chat' | 'coding' = 'chat',
  user_id?: string,
  title?: string,
  metadata?: any
): Promise<SessionInfo> {
  const thread_id = uuidv4();
  const now = new Date();

  const sessionInfo: SessionInfo = {
    thread_id,
    user_id,
    session_type: sessionType,
    title: title || `${sessionType === 'chat' ? '聊天' : '编程'}会话 - ${now.toLocaleString()}`,
    description: '',
    tags: [],

    created_at: now,
    last_accessed: now,
    updated_at: now,

    message_count: 0,
    checkpoint_count: 0,
    total_tokens: 0,

    status: 'active',
    is_favorite: false,

    metadata: {
      model_config: metadata?.model_config,
      client_info: metadata?.client_info,
      performance: {
        avg_response_time: 0,
        total_duration: 0
      }
    }
  };

  // 保存到数据库
  await saveSessionInfo(sessionInfo);

  console.log(`SessionManager: 创建完整会话信息 ${thread_id}`);
  return sessionInfo;
}

/**
 * 获取会话配置
 */
export function getSessionConfig(thread_id: string, checkpoint_ns: string = '', user_id?: string): SessionConfig {
  return {
    thread_id,
    checkpoint_ns,
    user_id
  };
}

/**
 * 保存会话信息到数据库
 */
export async function saveSessionInfo(sessionInfo: SessionInfo): Promise<void> {
  try {
    const client = await getMongoClient();
    const db = client.db();
    const collection = db.collection('sessions');

    // 使用thread_id作为字符串主键
    await collection.insertOne({
      ...sessionInfo,
      thread_id: sessionInfo.thread_id
    });

    console.log(`SessionManager: 会话信息已保存 ${sessionInfo.thread_id}`);
  } catch (error) {
    console.error(`SessionManager: 保存会话信息失败 ${sessionInfo.thread_id}`, error);
    throw error;
  }
}

/**
 * 更新会话信息
 */
export async function updateSessionInfo(thread_id: string, updates: Partial<SessionInfo>): Promise<void> {
  try {
    const client = await getMongoClient();
    const db = client.db();
    const collection = db.collection('sessions');

    await collection.updateOne(
      { thread_id: thread_id },
      {
        $set: {
          ...updates,
          updated_at: new Date()
        }
      }
    );

    console.log(`SessionManager: 会话信息已更新 ${thread_id}`);
  } catch (error) {
    console.error(`SessionManager: 更新会话信息失败 ${thread_id}`, error);
    throw error;
  }
}

/**
 * 获取会话信息
 */
export async function getSessionInfo(thread_id: string): Promise<SessionInfo | null> {
  try {
    const client = await getMongoClient();
    const db = client.db();
    const collection = db.collection('sessions');

    const session = await collection.findOne({ thread_id: thread_id });
    if (!session) {
      return null;
    }

    // 转换MongoDB文档为SessionInfo，确保日期类型正确
    return {
      thread_id: session.thread_id,
      user_id: session.user_id,
      session_type: session.session_type,
      title: session.title,
      description: session.description,
      tags: session.tags || [],
      created_at: new Date(session.created_at),
      last_accessed: new Date(session.last_accessed),
      updated_at: new Date(session.updated_at),
      message_count: session.message_count || 0,
      checkpoint_count: session.checkpoint_count || 0,
      total_tokens: session.total_tokens,
      status: session.status || 'active',
      is_favorite: session.is_favorite || false,
      metadata: session.metadata || {},
      last_message: session.last_message
    } as SessionInfo;
  } catch (error) {
    console.error(`SessionManager: 获取会话信息失败 ${thread_id}`, error);
    return null;
  }
}

/**
 * 验证会话是否存在
 */
export async function validateSession(thread_id: string): Promise<boolean> {
  try {
    // 首先检查会话信息是否存在
    const sessionInfo = await getSessionInfo(thread_id);
    if (!sessionInfo) {
      return false;
    }

    // 然后检查checkpointer中是否有数据
    const checkpointer = await getMongoCheckpointer();
    const config = { configurable: { thread_id } };

    // 尝试获取会话状态
    const checkpoint = await checkpointer.getTuple(config);
    return checkpoint !== undefined;
  } catch (error) {
    console.error(`SessionManager: 验证会话 ${thread_id} 失败`, error);
    return false;
  }
}

/**
 * 获取会话列表
 */
export async function getSessionList(params: SessionListParams = {}): Promise<{
  sessions: SessionInfo[];
  total: number;
  page: number;
  limit: number;
}> {
  try {
    const client = await getMongoClient();
    const db = client.db();
    const collection = db.collection('sessions');

    // 构建查询条件
    const filter: any = {};

    if (params.user_id) {
      filter.user_id = params.user_id;
    }

    if (params.session_type) {
      filter.session_type = params.session_type;
    }

    if (params.status) {
      filter.status = params.status;
    } else {
      // 默认只显示活跃会话
      filter.status = { $ne: 'deleted' };
    }

    if (params.tags && params.tags.length > 0) {
      filter.tags = { $in: params.tags };
    }

    if (params.search) {
      filter.$or = [
        { title: { $regex: params.search, $options: 'i' } },
        { description: { $regex: params.search, $options: 'i' } }
      ];
    }

    // 分页参数
    const page = params.page || 1;
    const limit = params.limit || 20;
    const skip = (page - 1) * limit;

    // 排序
    const sort: any = {};
    const sortBy = params.sort_by || 'last_accessed';
    const sortOrder = params.sort_order || 'desc';
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // 执行查询
    const [sessions, total] = await Promise.all([
      collection.find(filter).sort(sort).skip(skip).limit(limit).toArray(),
      collection.countDocuments(filter)
    ]);

    // 转换为SessionInfo格式
    const sessionInfos = sessions.map(session => ({
      thread_id: session.thread_id,
      user_id: session.user_id,
      session_type: session.session_type,
      title: session.title,
      description: session.description,
      tags: session.tags || [],
      created_at: new Date(session.created_at),
      last_accessed: new Date(session.last_accessed),
      updated_at: new Date(session.updated_at),
      message_count: session.message_count || 0,
      checkpoint_count: session.checkpoint_count || 0,
      total_tokens: session.total_tokens,
      status: session.status || 'active',
      is_favorite: session.is_favorite || false,
      metadata: session.metadata || {},
      last_message: session.last_message
    })) as SessionInfo[];

    return {
      sessions: sessionInfos,
      total,
      page,
      limit
    };
  } catch (error) {
    console.error('SessionManager: 获取会话列表失败', error);
    return {
      sessions: [],
      total: 0,
      page: params.page || 1,
      limit: params.limit || 20
    };
  }
}

/**
 * 获取会话历史信息（包含完整的状态数据）
 */
export async function getSessionHistory(thread_id: string, limit: number = 10) {
  try {
    const checkpointer = await getMongoCheckpointer();
    const config = { configurable: { thread_id } };

    // 获取会话历史
    const history = [];
    let count = 0;

    for await (const checkpoint of checkpointer.list(config)) {
      if (count >= limit) break;

      // 获取完整的检查点数据，包括状态信息
      const checkpointData = {
        checkpoint_id: checkpoint.config.configurable?.checkpoint_id,
        timestamp: checkpoint.checkpoint?.ts,
        step: checkpoint.metadata?.step,
        source: checkpoint.metadata?.source,
        // 添加状态数据，包含messages等信息
        state: checkpoint.checkpoint?.channel_values || {},
        // 添加写入操作信息（使用正确的属性名）
        pending_writes: (checkpoint.checkpoint as any)?.pending_writes || [],
        // 添加版本信息（使用正确的属性名）
        version: checkpoint.checkpoint?.versions_seen || {}
      };

      history.push(checkpointData);
      count++;
    }

    return history;
  } catch (error) {
    console.error(`SessionManager: 获取会话历史 ${thread_id} 失败`, error);
    return [];
  }
}

/**
 * 获取当前会话状态（最新的检查点状态）
 */
export async function getCurrentSessionState(thread_id: string) {
  try {
    const checkpointer = await getMongoCheckpointer();
    const config = { configurable: { thread_id } };

    // 获取最新的检查点
    const checkpoint = await checkpointer.getTuple(config);

    if (!checkpoint) {
      return null;
    }

    return {
      checkpoint_id: checkpoint.config.configurable?.checkpoint_id,
      timestamp: checkpoint.checkpoint?.ts,
      step: checkpoint.metadata?.step,
      source: checkpoint.metadata?.source,
      // 当前状态数据
      state: checkpoint.checkpoint?.channel_values || {},
      // 待处理的写入操作（使用正确的属性名）
      pending_writes: (checkpoint.checkpoint as any)?.pending_writes || [],
      // 版本信息（使用正确的属性名）
      version: checkpoint.checkpoint?.versions_seen || {},
      // 元数据
      metadata: checkpoint.metadata || {}
    };
  } catch (error) {
    console.error(`SessionManager: 获取当前会话状态 ${thread_id} 失败`, error);
    return null;
  }
}

/**
 * 获取会话的消息历史（从状态中提取messages）
 */
export async function getSessionMessages(thread_id: string, limit: number = 50) {
  try {
    const currentState = await getCurrentSessionState(thread_id);

    if (!currentState || !currentState.state) {
      return [];
    }

    // 从状态中提取messages，确保是数组类型
    const messages = Array.isArray(currentState.state.messages) ? currentState.state.messages : [];

    // 限制返回的消息数量
    return messages.slice(-limit).map((msg: any, index: number) => {
      const msgType = msg._type || msg.type || 'unknown';

      // 更准确的角色映射
      let role: string;
      if (msgType === 'human') {
        role = 'user';
      } else if (msgType === 'ai') {
        role = 'assistant';
      } else if (msgType === 'system') {
        role = 'system';
      } else if (msgType === 'tool') {
        role = 'system'; // 工具消息通常作为系统消息处理
      } else {
        // 对于未知类型，尝试从其他字段推断
        role = msg.role || 'unknown';
      }

      return {
        id: index,
        type: msgType,
        content: msg.content || '',
        timestamp: msg.timestamp || currentState.timestamp,
        role: role
      };
    });
  } catch (error) {
    console.error(`SessionManager: 获取会话消息 ${thread_id} 失败`, error);
    return [];
  }
}

/**
 * 删除会话
 */
export async function deleteSession(thread_id: string): Promise<boolean> {
  try {
    const checkpointer = await getMongoCheckpointer();
    await checkpointer.deleteThread(thread_id);
    
    console.log(`SessionManager: 会话 ${thread_id} 已删除`);
    return true;
  } catch (error) {
    console.error(`SessionManager: 删除会话 ${thread_id} 失败`, error);
    return false;
  }
}

/**
 * 获取会话统计信息
 */
export async function getSessionStats(user_id?: string): Promise<SessionStats> {
  try {
    const client = await getMongoClient();
    const db = client.db();
    const collection = db.collection('sessions');

    // 构建基础过滤条件
    const baseFilter: any = { status: { $ne: 'deleted' } };
    if (user_id) {
      baseFilter.user_id = user_id;
    }

    // 聚合查询获取统计信息
    const stats = await collection.aggregate([
      { $match: baseFilter },
      {
        $group: {
          _id: null,
          total_sessions: { $sum: 1 },
          active_sessions: {
            $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
          },
          archived_sessions: {
            $sum: { $cond: [{ $eq: ['$status', 'archived'] }, 1, 0] }
          },
          total_messages: { $sum: '$message_count' },
          chat_sessions: {
            $sum: { $cond: [{ $eq: ['$session_type', 'chat'] }, 1, 0] }
          },
          coding_sessions: {
            $sum: { $cond: [{ $eq: ['$session_type', 'coding'] }, 1, 0] }
          }
        }
      }
    ]).toArray();

    const result = stats[0] || {
      total_sessions: 0,
      active_sessions: 0,
      archived_sessions: 0,
      total_messages: 0,
      chat_sessions: 0,
      coding_sessions: 0
    };

    // 计算平均消息数
    const avg_messages_per_session = result.total_sessions > 0
      ? Math.round(result.total_messages / result.total_sessions * 100) / 100
      : 0;

    // 获取最活跃的日期
    const mostActiveDay = await getMostActiveDay(baseFilter, collection);

    return {
      total_sessions: result.total_sessions,
      active_sessions: result.active_sessions,
      archived_sessions: result.archived_sessions,
      total_messages: result.total_messages,
      avg_messages_per_session,
      most_active_day: mostActiveDay,
      session_types: {
        chat: result.chat_sessions,
        coding: result.coding_sessions
      }
    };
  } catch (error) {
    console.error('SessionManager: 获取会话统计失败', error);
    return {
      total_sessions: 0,
      active_sessions: 0,
      archived_sessions: 0,
      total_messages: 0,
      avg_messages_per_session: 0,
      most_active_day: new Date().toISOString().split('T')[0],
      session_types: {
        chat: 0,
        coding: 0
      }
    };
  }
}

/**
 * 获取最活跃的日期
 */
async function getMostActiveDay(baseFilter: any, collection: any): Promise<string> {
  try {
    const dailyStats = await collection.aggregate([
      { $match: baseFilter },
      {
        $group: {
          _id: {
            $dateToString: {
              format: '%Y-%m-%d',
              date: '$created_at'
            }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { count: -1 } },
      { $limit: 1 }
    ]).toArray();

    return dailyStats[0]?._id || new Date().toISOString().split('T')[0];
  } catch (error) {
    console.error('SessionManager: 获取最活跃日期失败', error);
    return new Date().toISOString().split('T')[0];
  }
}

/**
 * 清理过期会话
 */
export async function cleanupExpiredSessions(maxAgeHours: number = 24): Promise<number> {
  try {
    const client = await getMongoClient();
    const db = client.db();
    const collection = db.collection('sessions');
    const cutoffTime = new Date(Date.now() - maxAgeHours * 60 * 60 * 1000);

    console.log(`SessionManager: 开始清理 ${maxAgeHours} 小时前的会话`);

    // 标记过期会话为已删除状态，而不是直接删除
    const result = await collection.updateMany(
      {
        last_accessed: { $lt: cutoffTime },
        status: { $ne: 'deleted' }
      },
      {
        $set: {
          status: 'deleted',
          updated_at: new Date()
        }
      }
    );

    console.log(`SessionManager: 已清理 ${result.modifiedCount} 个过期会话`);
    return result.modifiedCount;
  } catch (error) {
    console.error('SessionManager: 清理过期会话失败', error);
    return 0;
  }
}

/**
 * 从请求中提取或创建thread_id
 */
export async function extractOrCreateThreadId(
  body: any,
  sessionType: 'chat' | 'coding' = 'chat'
): Promise<string> {
  // 优先使用请求中的thread_id
  if (body.thread_id && typeof body.thread_id === 'string') {
    console.log(`SessionManager: 使用现有会话 ${body.thread_id}`);
    return body.thread_id;
  }

  // 如果没有提供thread_id，创建新会话并保存到数据库
  const sessionInfo = await createSessionInfo(
    sessionType,
    body.user_id,
    body.title,
    body.metadata
  );
  console.log(`SessionManager: 创建新会话并保存到数据库 ${sessionInfo.thread_id}`);
  return sessionInfo.thread_id;
}

/**
 * 构建LangGraph配置对象
 */
export function buildGraphConfig(thread_id: string, checkpoint_ns: string = ''): any {
  return {
    configurable: {
      thread_id,
      checkpoint_ns
    }
  };
}
